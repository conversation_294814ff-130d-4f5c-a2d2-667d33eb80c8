ENV=production

#DATABASE_URL="postgresql://postgres:<EMAIL>:38684/railway?schema=public"
DATABASE_URL="postgresql://postgres:<EMAIL>:22236/railway?schema=public"
SOCKET_IO_PATH=/api/socket/io

JWT_SECRET="secret"
JWT_EXPIRES_IN="24h"

# AWS S3 Configuration
# AWS_ACCESS_KEY_ID=********************
# AWS_SECRET_ACCESS_KEY=xi2jWWXA7U5kyyuO8fVEdRZDthH5LQ+kkPHLgrT9
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=PEczrQY04ytO7H92/sGEQjaAbI7Q+xxY6iNnbIST
AWS_REGION=ap-southeast-1
# AWS_S3_BUCKET_NAME=kabaodev-bucket-20250529104000
AWS_S3_BUCKET_NAME=hiasangma

# NEXT_PUBLIC_AWS_CDN_URL=https://d2zrumgoux7beg.cloudfront.net
NEXT_PUBLIC_AWS_CDN_URL=https://d1y2ljh841nvy9.cloudfront.net

NEXT_PUBLIC_LITE_LLM_API_KEY=sk-1E8AEDgVP0qWoHXIP2VBAQ
NEXT_PUBLIC_LITE_LLM_ENDPOINT=https://llm-dev.lucablock.io


NEXT_PUBLIC_SOCKET_URL=https://hia-sang-ma-socket-production.up.railway.app