'use client';

import React, { forwardRef, ForwardedRef, useEffect } from 'react';
import styled from 'styled-components';
import { Trash2, FileEdit, X } from 'lucide-react';
import { appTheme } from '@/app/theme';
import useUserStore from '@/store/userStore';

// Types
interface Message {
  id: string;
  senderId: string;
  senderName: string;
  content: string;
  timestamp: string;
  type: 'text' | 'image' | 'file' | 'sticker' | 'link';
  status: 'sending' | 'delivered' | 'read' | 'failed';
  imageUrl?: string;
  user?: {
    id: number;
    firstName: string;
    lastName: string;
    imageUrl?: string;
  };
}

interface MessageActionPopoverProps {
  message: Message;
  onCreateTask: (message: Message) => void;
  onDeleteMessage: (message: Message) => void;
  isOpen: boolean;
  onClose: () => void;
  isOwnMessage?: boolean;
}

// Styled components
const PopoverContainer = styled.div<{ $isOpen: boolean; $isOwnMessage: boolean }>`
  position: absolute;
  bottom: 32px;
  ${props => props.$isOwnMessage ? 'left: -160px;' : 'right: -160px;'}
  background-color: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.md};
  box-shadow: ${appTheme.shadows.lg};
  overflow: hidden;
  display: ${props => (props.$isOpen ? 'block' : 'none')};
  z-index: 100;
  width: 180px;
  animation: ${props => props.$isOpen ? 'popoverFadeIn 0.2s ease-out' : 'none'};
  transform-origin: ${props => props.$isOwnMessage ? 'bottom left' : 'bottom right'};

  /* Responsive positioning for mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    bottom: 36px;
    ${props => props.$isOwnMessage ? 'left: -140px;' : 'right: -140px;'}
    width: 160px;
  }

  @keyframes popoverFadeIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  &::before {
    content: '';
    position: absolute;
    bottom: -6px;
    ${props => props.$isOwnMessage ? 'left: 172px;' : 'right: 172px;'}
    transform: rotate(45deg);
    width: 12px;
    height: 12px;
    background-color: ${appTheme.colors.background.main};
    border-radius: 2px;
    box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.05);
  }
`;

const PopoverHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border-bottom: 1px solid ${appTheme.colors.border};
`;

const PopoverTitle = styled.h3`
  margin: 0;
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: ${appTheme.colors.text.primary};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${appTheme.colors.text.secondary};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px;
  border-radius: ${appTheme.borderRadius.sm};
  
  &:hover {
    background-color: ${appTheme.colors.background.light};
    color: ${appTheme.colors.text.primary};
  }
`;

const ActionsList = styled.ul`
  list-style: none;
  margin: 0;
  padding: 0;
`;

const ActionItem = styled.li`
  padding: 0;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  width: 100%;
  text-align: left;
  background: none;
  border: none;
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  cursor: pointer;
  color: ${appTheme.colors.text.primary};
  font-size: ${appTheme.typography.fontSizes.sm};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${appTheme.colors.background.light};
    transform: translateX(2px);
  }

  &:focus {
    outline: 2px solid ${appTheme.colors.primary};
    outline-offset: -2px;
    background-color: ${appTheme.colors.background.light};
  }

  &.delete {
    color: ${appTheme.colors.error.main};

    &:hover {
      background-color: ${appTheme.colors.error.light};
      color: ${appTheme.colors.error.main};
    }

    &:focus {
      outline-color: ${appTheme.colors.error.main};
    }
  }
`;

const ActionIcon = styled.span`
  display: flex;
  align-items: center;
  margin-right: ${appTheme.spacing.sm};
`;

// Removed TriggerButton as we'll use the message bubble as the trigger

const MessageActionPopover = forwardRef<HTMLDivElement, MessageActionPopoverProps>(
  ({ message, onCreateTask, onDeleteMessage, isOpen, onClose, isOwnMessage: propIsOwnMessage }, ref: ForwardedRef<HTMLDivElement>) => {
    const { userData } = useUserStore();

    // Check if current user is the message sender (use prop if provided, otherwise calculate)
    const isOwnMessage = propIsOwnMessage !== undefined ? propIsOwnMessage : (userData ? String(message.senderId) === String(userData.id) : false);

    // Handle keyboard events
    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    // Close popover when clicking outside
    useEffect(() => {
      if (!isOpen) return;

      const handleClickOutside = (e: MouseEvent) => {
        if (ref && 'current' in ref && ref.current && !ref.current.contains(e.target as Node)) {
          onClose();
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [isOpen, onClose, ref]);

    return (
      <div ref={ref} style={{ position: 'relative' }}>
        <PopoverContainer
          $isOpen={isOpen}
          $isOwnMessage={isOwnMessage}
          onKeyDown={handleKeyDown}
          role="menu"
          aria-label="Message actions menu"
        >
          <PopoverHeader>
            <PopoverTitle>Message Actions</PopoverTitle>
            <CloseButton onClick={onClose} aria-label="Close menu">
              <X size={14} />
            </CloseButton>
          </PopoverHeader>

          <ActionsList>
            <ActionItem>
              <ActionButton
                onClick={() => {
                  onCreateTask(message);
                  onClose();
                }}
                role="menuitem"
                tabIndex={isOpen ? 0 : -1}
              >
                <ActionIcon>
                  <FileEdit size={16} />
                </ActionIcon>
                Create Task
              </ActionButton>
            </ActionItem>



            {isOwnMessage && (
              <ActionItem>
                <ActionButton
                  className="delete"
                  onClick={() => {
                    onDeleteMessage(message);
                    onClose();
                  }}
                  role="menuitem"
                  tabIndex={isOpen ? 0 : -1}
                >
                  <ActionIcon>
                    <Trash2 size={16} />
                  </ActionIcon>
                  Delete Message
                </ActionButton>
              </ActionItem>
            )}
          </ActionsList>
        </PopoverContainer>
      </div>
    );
  }
);

MessageActionPopover.displayName = 'MessageActionPopover';

export default MessageActionPopover;
