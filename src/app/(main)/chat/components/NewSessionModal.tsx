'use client';

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { X, Bot } from 'lucide-react';
import { appTheme } from '@/app/theme';
import { framework, ai_models } from '@/utils/ai-assistant';

interface NewSessionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onGenerate: (messageText: string, selectedFramework: any, selectedAiModel: any) => void;
  initialText?: string;
  loading?: boolean;
}

const ModalOverlay = styled.div<{ $isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: ${props => props.$isOpen ? 'flex' : 'none'};
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
`;

const ModalContent = styled.div`
  background: ${appTheme.colors.background.main};
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid ${appTheme.colors.border};
`;

const ModalHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${appTheme.spacing.lg};
  border-bottom: 1px solid ${appTheme.colors.border};
  background: ${appTheme.colors.background.lighter};
`;

const ModalTitle = styled.h2`
  font-size: 18px;
  font-weight: 600;
  color: ${appTheme.colors.text.primary};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: ${appTheme.colors.text.secondary};
  cursor: pointer;
  padding: ${appTheme.spacing.xs};
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background: ${appTheme.colors.background.lighter};
    color: ${appTheme.colors.text.primary};
  }
`;

const ModalBody = styled.div`
  padding: ${appTheme.spacing.lg};
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 120px;
  padding: ${appTheme.spacing.md};
  border: 1px solid ${appTheme.colors.border};
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  color: ${appTheme.colors.text.primary};
  background: ${appTheme.colors.background.main};
  resize: vertical;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${appTheme.colors.primary};
    box-shadow: 0 0 0 3px ${appTheme.colors.primary}20;
  }

  &::placeholder {
    color: ${appTheme.colors.text.secondary};
  }
`;

const FormGroup = styled.div`
  margin-bottom: ${appTheme.spacing.md};
`;

const FormLabel = styled.label`
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: ${appTheme.colors.text.primary};
  margin-bottom: ${appTheme.spacing.xs};
`;

const Select = styled.select`
  width: 100%;
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border: 1px solid ${appTheme.colors.border};
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  color: ${appTheme.colors.text.primary};
  background: ${appTheme.colors.background.main};
  transition: border-color 0.2s ease;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: ${appTheme.colors.primary};
    box-shadow: 0 0 0 3px ${appTheme.colors.primary}20;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const SelectionsContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${appTheme.spacing.md};
  margin-bottom: ${appTheme.spacing.md};
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${appTheme.spacing.sm};
  padding: ${appTheme.spacing.lg};
  border-top: 1px solid ${appTheme.colors.border};
  background: ${appTheme.colors.background.lighter};
`;

const Button = styled.button<{ $variant?: 'primary' | 'secondary' }>`
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};

  ${props => props.$variant === 'primary' ? `
    background: ${appTheme.colors.primary};
    color: white;
    border-color: ${appTheme.colors.primary};

    &:hover:not(:disabled) {
      background: ${appTheme.colors.primaryHover};
      border-color: ${appTheme.colors.primaryHover};
    }

    &:disabled {
      background: ${appTheme.colors.text.secondary};
      border-color: ${appTheme.colors.text.secondary};
      cursor: not-allowed;
    }
  ` : `
    background: ${appTheme.colors.background.main};
    color: ${appTheme.colors.text.primary};
    border-color: ${appTheme.colors.border};

    &:hover {
      background: ${appTheme.colors.background.lighter};
    }
  `}
`;

const ErrorMessage = styled.div`
  color: ${appTheme.colors.error};
  font-size: 14px;
  margin-bottom: ${appTheme.spacing.md};
  padding: ${appTheme.spacing.sm};
  background: ${appTheme.colors.error}10;
  border: 1px solid ${appTheme.colors.error}30;
  border-radius: 6px;
`;

export default function NewSessionModal({
  isOpen,
  onClose,
  onGenerate,
  initialText = '',
  loading = false,
}: NewSessionModalProps) {
  const [messageText, setMessageText] = useState('');
  const [selectedFramework, setSelectedFramework] = useState(framework[0]);
  const [selectedAiModel, setSelectedAiModel] = useState(ai_models[0]);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen) {
      setMessageText(initialText);
      setSelectedFramework(framework[0]);
      setSelectedAiModel(ai_models[0]);
      setError('');
    }
  }, [isOpen, initialText]);

  const handleGenerate = () => {
    const trimmedText = messageText.trim();
    if (!trimmedText) {
      setError('กรุณาใส่ข้อความเพื่อเริ่มการสนทนากับ AI Assistant');
      return;
    }

    setError('');
    onGenerate(trimmedText, selectedFramework, selectedAiModel);
  };

  const handleFrameworkChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const frameworkName = e.target.value;
    const selected = framework.find((f: any) => f.name === frameworkName);
    if (selected) {
      setSelectedFramework(selected);
    }
  };

  const handleAiModelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const modelName = e.target.value;
    const selected = ai_models.find((m: any) => m.name === modelName);
    if (selected) {
      setSelectedAiModel(selected);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleGenerate();
    }
  };

  if (!isOpen) return null;

  return (
    <ModalOverlay $isOpen={isOpen} onClick={onClose}>
      <ModalContent onClick={e => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>
            <Bot size={20} />
            สร้าง session ใหม่
          </ModalTitle>
          <CloseButton onClick={onClose}>
            <X size={20} />
          </CloseButton>
        </ModalHeader>

        <ModalBody>
          {error && <ErrorMessage>{error}</ErrorMessage>}

          <SelectionsContainer>
            <FormGroup>
              <FormLabel>Template</FormLabel>
              <Select
                value={selectedFramework.name}
                onChange={handleFrameworkChange}
                disabled={loading}
              >
                {framework.map((f: any) => (
                  <option key={f.name} value={f.name}>
                    {f.displayName}
                  </option>
                ))}
              </Select>
            </FormGroup>

            <FormGroup>
              <FormLabel>AI Model</FormLabel>
              <Select
                value={selectedAiModel.name}
                onChange={handleAiModelChange}
                disabled={loading}
              >
                {ai_models.map((model: any) => (
                  <option key={model.name} value={model.name}>
                    {model.displayName}
                  </option>
                ))}
              </Select>
            </FormGroup>
          </SelectionsContainer>

          <FormGroup>
            <FormLabel>ข้อความ</FormLabel>
            <TextArea
              placeholder="ใส่ข้อความของคุณเพื่อเริ่มการสนทนากับ AI Assistant..."
              value={messageText}
              onChange={e => setMessageText(e.target.value)}
              onKeyDown={handleKeyDown}
              disabled={loading}
            />
          </FormGroup>
        </ModalBody>

        <ModalFooter>
          <Button onClick={onClose} disabled={loading}>
            ยกเลิก
          </Button>
          <Button
            $variant="primary"
            onClick={handleGenerate}
            disabled={loading || !messageText.trim()}
          >
            {loading ? 'กำลังสร้าง...' : 'สร้าง'}
          </Button>
        </ModalFooter>
      </ModalContent>
    </ModalOverlay>
  );
}
